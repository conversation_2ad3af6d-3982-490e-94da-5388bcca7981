import { loadExternalResource } from '@/lib/utils'
import { useEffect } from 'react'
// import AOS from 'aos'

/**
 * 加载滚动动画
 * 改从外部CDN读取
 * https://michalsnik.github.io/aos/
 */
export default function AOSAnimation() {
  const initAOS = async () => {
    try {
      await Promise.all([
        loadExternalResource('/js/aos.js', 'js'),
        loadExternalResource('/css/aos.css', 'css')
      ])

      if (window.AOS) {
        window.AOS.init({
          duration: 800,
          once: true, // 只执行一次动画，提升性能
          offset: 100
        })
      }
    } catch (error) {
      console.warn('Failed to load AOS animation:', error.message)
    }
  }

  useEffect(() => {
    // 延迟初始化AOS，避免阻塞页面渲染
    const timer = setTimeout(() => {
      initAOS()
    }, 3000)

    return () => clearTimeout(timer)
  }, [])
}
