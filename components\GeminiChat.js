// C:\Users\<USER>\Downloads\wobshare\components\GeminiChat.js (Function Calling版本)

import { siteConfig } from '@/lib/config'
import { useState, useRef, useEffect } from 'react'

export default function GeminiChat() {
  const [isOpen, setIsOpen] = useState(false)
  
  // messages现在需要遵循Gemini API的格式: { role: 'user' | 'model', parts: [{ text: '...' }] }
  const [messages, setMessages] = useState([])
  
  const [input, setInput] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const chatBoxRef = useRef(null)

  useEffect(() => {
    if (chatBoxRef.current) {
      chatBoxRef.current.scrollTop = chatBoxRef.current.scrollHeight
    }
  }, [messages])

  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return

    // 将新消息添加到对话历史中
    const newUserMessage = { role: 'user', parts: [{ text: input }] }
    const newMessages = [...messages, newUserMessage]
    
    setMessages(newMessages)
    setInput('')
    setIsLoading(true)

    try {
      // 调用我们的Cloudflare Worker代理，这次我们发送完整的对话历史
      // Worker的URL现在指向你代理的根域名
      const response = await fetch(siteConfig('GEMINI_API_URL'), {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        // 发送包含历史记录的对象
        body: JSON.stringify({ messages: newMessages }) 
      })

      if (!response.ok) {
        throw new Error('API request failed')
      }

      const data = await response.json()
      
      // 从返回结果中提取模型的回复
      const botResponsePart = data.candidates?.[0]?.content
      if (botResponsePart) {
        // 将模型的回复也添加到对话历史中
        setMessages([...newMessages, botResponsePart])
      } else {
        throw new Error('Invalid response from API')
      }

    } catch (error) {
      console.error('Failed to fetch from Gemini API:', error)
      const errorResponse = {
        role: 'model',
        parts: [{ text: '抱歉，我好像出了一点问题...' }]
      }
      setMessages([...newMessages, errorResponse])
    } finally {
      setIsLoading(false)
    }
  }

  if (!siteConfig('GEMINI_CHAT_ENABLED') || !siteConfig('GEMINI_API_URL')) {
    return null
  }

  // 渲染消息时，需要从 parts 数组中取 text
  const renderMessage = (msg, index) => {
    const textContent = msg.parts.map(part => part.text).join('') // 简单处理，只显示text部分
    if (!textContent) return null // 如果是函数调用等无文本内容的部分，则不渲染

    return (
        <div key={index} className={`my-2 ${msg.role === 'user' ? 'text-right' : 'text-left'}`}>
            <span className={`inline-block p-2 rounded-lg ${msg.role === 'user' ? 'bg-blue-100' : 'bg-gray-200'}`}>
                {textContent}
            </span>
        </div>
    )
  }

  return (
    <>
      <div
        className="fixed bottom-8 right-8 w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white text-3xl cursor-pointer shadow-lg z-50"
        onClick={() => setIsOpen(!isOpen)}
      >
        💬
      </div>

      {isOpen && (
        <div className="fixed bottom-28 right-8 w-80 h-96 bg-white rounded-lg shadow-2xl flex flex-col z-50">
          <div className="p-3 bg-blue-500 text-white rounded-t-lg">AI 助手</div>
          <div ref={chatBoxRef} className="flex-1 p-4 overflow-y-auto">
            {messages.length === 0 && <div className="text-gray-400 text-sm text-center">可以发链接给我总结哦！</div>}
            {messages.map(renderMessage)}
            {isLoading && <div className="text-left"><span className="inline-block p-2 rounded-lg bg-gray-200">思考中...</span></div>}
          </div>
          <div className="p-2 border-t border-gray-200 flex">
            <input
              type="text"
              className="flex-1 p-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              placeholder="输入消息或链接..."
            />
            <button
              className="ml-2 px-4 py-2 bg-blue-500 text-white rounded-lg disabled:bg-blue-300"
              onClick={handleSendMessage}
              disabled={isLoading}
            >
              发送
            </button>
          </div>
        </div>
      )}
    </>
  )
}
