import { siteConfig } from '@/lib/config'
import { getAllProxiedUrls } from '@/lib/notion-image-proxy'
import Head from 'next/head'
import { useEffect, useRef, useState } from 'react'

/**
 * 图片懒加载
 * @param {*} param0
 * @returns
 */
export default function LazyImage({
  priority,
  id,
  src,
  alt,
  placeholderSrc,
  className,
  width,
  height,
  title,
  onLoad,
  onClick,
  style
}) {
  const maxWidth = siteConfig('IMAGE_COMPRESS_WIDTH')
  const defaultPlaceholderSrc = siteConfig('IMG_LAZY_LOAD_PLACEHOLDER')
  const imageRef = useRef(null)
  const [currentSrc, setCurrentSrc] = useState(
    placeholderSrc || defaultPlaceholderSrc
  )

  /**
   * 占位图加载成功
   */
  const handleThumbnailLoaded = () => {
    if (typeof onLoad === 'function') {
      // onLoad() // 触发传递的onLoad回调函数
    }
  }
  // 原图加载完成
  const handleImageLoaded = img => {
    if (typeof onLoad === 'function') {
      onLoad() // 触发传递的onLoad回调函数
    }
    // 移除占位符类名
    if (imageRef.current) {
      imageRef.current.classList.remove('lazy-image-placeholder')
    }
  }
  /**
   * 图片加载失败回调 - 支持多个备用代理
   */
  const handleImageError = (retryCount = 0) => {
    console.warn(`图片加载失败 (尝试 ${retryCount + 1}):`, src)

    // 获取所有可用的代理URL
    const proxiedUrls = getAllProxiedUrls(src)

    // 尝试使用备用代理
    if (retryCount < proxiedUrls.length) {
      const backupUrl = proxiedUrls[retryCount]
      console.log(`尝试备用代理 ${retryCount + 1}:`, backupUrl)

      const img = new Image()
      img.onload = () => {
        setCurrentSrc(backupUrl)
        handleImageLoaded(backupUrl)
      }
      img.onerror = () => handleImageError(retryCount + 1)
      img.src = backupUrl
      return
    }

    if (imageRef.current) {
      // 所有代理都失败，尝试加载占位符
      if (imageRef.current.src !== placeholderSrc && placeholderSrc) {
        imageRef.current.src = placeholderSrc
      } else {
        imageRef.current.src = defaultPlaceholderSrc
      }
      // 移除占位符类名
      if (imageRef.current) {
        imageRef.current.classList.remove('lazy-image-placeholder')
      }
    }
  }

  useEffect(() => {
    if (!src) return

    const adjustedImageSrc =
      adjustImgSize(src, maxWidth) || defaultPlaceholderSrc

    let observer = null
    let timeoutId = null

    const loadImage = () => {
      const img = new Image()

      // 添加超时处理
      timeoutId = setTimeout(() => {
        console.warn('Image load timeout:', adjustedImageSrc)
        handleImageError()
      }, 15000) // 15秒超时

      img.onload = () => {
        clearTimeout(timeoutId)
        setCurrentSrc(adjustedImageSrc)
        handleImageLoaded(adjustedImageSrc)
      }

      img.onerror = () => {
        clearTimeout(timeoutId)
        handleImageError()
      }

      img.src = adjustedImageSrc
    }

    observer = new IntersectionObserver(
      entries => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            loadImage()
            observer.unobserve(entry.target)
          }
        })
      },
      { rootMargin: '100px 0px' } // 提前加载距离增加到100px
    )

    if (imageRef.current) {
      observer.observe(imageRef.current)
    }

    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId)
      }
      if (observer) {
        observer.disconnect()
      }
    }
  }, [src, maxWidth])

  // 动态添加width、height和className属性，仅在它们为有效值时添加
  const imgProps = {
    ref: imageRef,
    src: currentSrc,
    'data-src': src, // 存储原始图片地址
    alt: alt || 'Lazy loaded image',
    onLoad: handleThumbnailLoaded,
    onError: handleImageError,
    className: `${className || ''} lazy-image-placeholder`,
    style,
    width: width || 'auto',
    height: height || 'auto',
    onClick
  }

  if (id) imgProps.id = id
  if (title) imgProps.title = title

  if (!src) {
    return null
  }

  return (
    <>
      {/* eslint-disable-next-line @next/next/no-img-element */}
      <img {...imgProps} />
      {/* 预加载 */}
      {priority && (
        <Head>
          <link rel='preload' as='image' href={adjustImgSize(src, maxWidth)} />
        </Head>
      )}
      <style>
        {` 
        .lazy-image-placeholder{
            background: 
                linear-gradient(90deg,#0001 33%,#0005 50%,#0001 66%)
                #f2f2f2;
            background-size:300% 100%;
            animation: l1 1s infinite linear;
            }
            @keyframes l1 {
            0% {background-position: right}
        }
        `}
      </style>
    </>
  )
}

/**
 * 根据窗口尺寸决定压缩图片宽度
 * @param {*} src
 * @param {*} maxWidth
 * @returns
 */
const adjustImgSize = (src, maxWidth) => {
  if (!src) {
    return null
  }
  const screenWidth =
    (typeof window !== 'undefined' && window?.screen?.width) || maxWidth

  // 屏幕尺寸大于默认图片尺寸，没必要再压缩
  if (screenWidth > maxWidth) {
    return src
  }

  // 正则表达式，用于匹配 URL 中的 width 参数
  const widthRegex = /width=\d+/
  // 正则表达式，用于匹配 URL 中的 w 参数
  const wRegex = /w=\d+/

  // 使用正则表达式替换 width/w 参数
  return src
    .replace(widthRegex, `width=${screenWidth}`)
    .replace(wRegex, `w=${screenWidth}`)
}
