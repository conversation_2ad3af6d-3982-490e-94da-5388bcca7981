import { siteConfig } from '@/lib/config'
import { loadExternalResource } from '@/lib/utils'
import { useEffect, useRef, useState } from 'react'

/**
 * 音乐播放器
 * @returns
 */
const Player = () => {
  const [player, setPlayer] = useState()
  const ref = useRef(null)
  const lrcType = JSON.parse(siteConfig('MUSIC_PLAYER_LRC_TYPE'))
  const playerVisible = JSON.parse(siteConfig('MUSIC_PLAYER_VISIBLE'))
  const autoPlay = JSON.parse(siteConfig('MUSIC_PLAYER_AUTO_PLAY'))
  const meting = JSON.parse(siteConfig('MUSIC_PLAYER_METING'))
  const order = siteConfig('MUSIC_PLAYER_ORDER')
  const audio = siteConfig('MUSIC_PLAYER_AUDIO_LIST')

  const musicPlayerEnable = siteConfig('MUSIC_PLAYER')
  const musicPlayerCDN = siteConfig('MUSIC_PLAYER_CDN_URL')
  const musicMetingEnable = siteConfig('MUSIC_PLAYER_METING')
  const musicMetingCDNUrl = siteConfig(
    'MUSIC_PLAYER_METING_CDN_URL',
    'https://cdnjs.cloudflare.com/ajax/libs/meting/2.0.1/Meting.min.js'
  )

  const initMusicPlayer = async () => {
    if (!musicPlayerEnable) {
      return
    }

    // 备用CDN列表
    const backupCDNs = [
      musicPlayerCDN,
      'https://cdn.jsdelivr.net/npm/aplayer@1.10.1/dist/APlayer.min.js',
      'https://cdnjs.cloudflare.com/ajax/libs/aplayer/1.10.1/APlayer.min.js'
    ]

    let loaded = false
    for (const cdn of backupCDNs) {
      try {
        await loadExternalResource(cdn, 'js')
        loaded = true
        break
      } catch (error) {
        console.warn('CDN加载失败，尝试备用CDN:', cdn, error)
      }
    }

    if (!loaded) {
      console.error('所有音乐播放器CDN都无法加载')
      return
    }

    if (musicMetingEnable) {
      const metingBackups = [
        musicMetingCDNUrl,
        'https://cdn.jsdelivr.net/npm/meting@2.0.1/dist/Meting.min.js',
        'https://unpkg.com/meting@2.0.1/dist/Meting.min.js'
      ]

      for (const metingCdn of metingBackups) {
        try {
          await loadExternalResource(metingCdn, 'js')
          break
        } catch (error) {
          console.warn('MetingJS CDN加载失败，尝试备用CDN:', metingCdn, error)
        }
      }
    }

    if (!meting && window.APlayer) {
      setPlayer(
        new window.APlayer({
          container: ref.current,
          fixed: true,
          lrcType: lrcType,
          autoplay: autoPlay,
          order: order,
          audio: audio
        })
      )
    }
  }

  useEffect(() => {
    initMusicPlayer()
    return () => {
      setPlayer(undefined)
    }
  }, [])

  // 加载CSS样式
  useEffect(() => {
    if (musicPlayerEnable) {
      const cssUrls = [
        'https://unpkg.com/aplayer@1.10.1/dist/APlayer.min.css',
        'https://cdn.jsdelivr.net/npm/aplayer@1.10.1/dist/APlayer.min.css',
        'https://cdnjs.cloudflare.com/ajax/libs/aplayer/1.10.1/APlayer.min.css'
      ]

      const loadCSS = async () => {
        for (const cssUrl of cssUrls) {
          try {
            await loadExternalResource(cssUrl, 'css')
            break
          } catch (error) {
            console.warn('APlayer CSS加载失败，尝试备用CDN:', cssUrl, error)
          }
        }
      }

      loadCSS()
    }
  }, [musicPlayerEnable])

  return (
    <div className={playerVisible ? 'visible' : 'invisible'}>
      {meting ? (
        <meting-js
          fixed='true'
          type='playlist'
          preload='auto'
          api={siteConfig(
            'MUSIC_PLAYER_METING_API',
            'https://api.i-meto.com/meting/api?server=:server&type=:type&id=:id&r=:r'
          )}
          autoplay={autoPlay}
          order={siteConfig('MUSIC_PLAYER_ORDER')}
          server={siteConfig('MUSIC_PLAYER_METING_SERVER')}
          id={siteConfig('MUSIC_PLAYER_METING_ID')}
        />
      ) : (
        <div ref={ref} data-player={player} />
      )}
    </div>
  )
}

export default Player
