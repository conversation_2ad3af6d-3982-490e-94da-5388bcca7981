/**
 * Notion图片代理工具
 * 专门处理中国大陆访问Notion图片的问题
 */

// 国内可访问的Notion代理服务列表
const NOTION_PROXY_HOSTS = [
  'https://notion-api.vercel.app',
  'https://notion-proxy.vercel.app',
  'https://notion.davidl.me',
  'https://notion-api.splitbee.io',
  'https://www.notion.so' // 原始地址作为最后备用
]

/**
 * 获取最佳的图片代理URL
 * @param {string} originalUrl 原始图片URL
 * @param {number} proxyIndex 代理索引，默认使用第一个
 * @returns {string} 代理后的URL
 */
export function getProxiedImageUrl(originalUrl, proxyIndex = 0) {
  if (!originalUrl || !originalUrl.includes('notion')) {
    return originalUrl
  }

  const proxyHost = NOTION_PROXY_HOSTS[proxyIndex] || NOTION_PROXY_HOSTS[0]

  // 对于Vercel代理，需要特殊处理URL格式
  if (proxyHost.includes('vercel.app')) {
    // 提取图片ID和参数
    const match = originalUrl.match(/\/image\/(.+)/)
    if (match) {
      return `${proxyHost}/api/image/${encodeURIComponent(match[1])}`
    }
  }

  return originalUrl.replace(/^https?:\/\/[^\/]+/, proxyHost)
}

/**
 * 获取所有可用的代理URL列表
 * @param {string} originalUrl 原始图片URL
 * @returns {string[]} 所有代理URL的数组
 */
export function getAllProxiedUrls(originalUrl) {
  if (!originalUrl || !originalUrl.includes('notion')) {
    return [originalUrl]
  }

  return NOTION_PROXY_HOSTS.map(host => 
    originalUrl.replace(/^https?:\/\/[^\/]+/, host)
  )
}

/**
 * 测试图片URL是否可访问
 * @param {string} url 图片URL
 * @param {number} timeout 超时时间（毫秒）
 * @returns {Promise<boolean>} 是否可访问
 */
export function testImageUrl(url, timeout = 5000) {
  return new Promise((resolve) => {
    const img = new Image()
    const timer = setTimeout(() => {
      resolve(false)
    }, timeout)

    img.onload = () => {
      clearTimeout(timer)
      resolve(true)
    }

    img.onerror = () => {
      clearTimeout(timer)
      resolve(false)
    }

    img.src = url
  })
}

/**
 * 智能选择最佳的图片代理
 * @param {string} originalUrl 原始图片URL
 * @returns {Promise<string>} 最佳的代理URL
 */
export async function getBestProxiedUrl(originalUrl) {
  if (!originalUrl || !originalUrl.includes('notion')) {
    return originalUrl
  }

  const proxiedUrls = getAllProxiedUrls(originalUrl)
  
  // 并发测试所有代理
  const testPromises = proxiedUrls.map(async (url, index) => {
    const isAccessible = await testImageUrl(url, 3000)
    return { url, index, isAccessible }
  })

  try {
    const results = await Promise.all(testPromises)
    
    // 找到第一个可访问的代理
    const bestProxy = results.find(result => result.isAccessible)
    
    if (bestProxy) {
      console.log(`选择代理 ${bestProxy.index + 1}:`, bestProxy.url)
      return bestProxy.url
    }
    
    // 如果都不可访问，返回第一个代理
    console.warn('所有代理都不可访问，使用默认代理')
    return proxiedUrls[0]
  } catch (error) {
    console.error('代理测试失败:', error)
    return proxiedUrls[0]
  }
}

/**
 * 缓存最佳代理的索引
 */
let bestProxyIndex = 0

/**
 * 获取缓存的最佳代理URL
 * @param {string} originalUrl 原始图片URL
 * @returns {string} 代理后的URL
 */
export function getCachedProxiedUrl(originalUrl) {
  return getProxiedImageUrl(originalUrl, bestProxyIndex)
}

/**
 * 更新最佳代理索引
 * @param {number} index 代理索引
 */
export function updateBestProxyIndex(index) {
  bestProxyIndex = index
  console.log(`更新最佳代理索引为: ${index + 1}`)
}
