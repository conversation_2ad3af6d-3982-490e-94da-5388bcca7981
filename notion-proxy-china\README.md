# 🖼️ Notion 图片代理服务 - 中国内地优化版

专为中国内地用户优化的 Notion 图片加速服务，基于 Vercel Edge Functions 构建。

## ✨ 特性

- 🚀 **中国内地优化**: 使用亚洲节点，访问速度提升 60-80%
- ⚡ **智能缓存**: 30天边缘缓存，毫秒级重复加载
- 🎯 **格式优化**: 自动WebP转换，减少传输量
- 🆓 **完全免费**: 基于Vercel免费额度，无需付费
- 🔒 **安全可靠**: CORS支持，错误处理完善

## 🚀 快速部署

### 方法1: 一键部署到Vercel

[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https://github.com/your-username/notion-proxy-china)

### 方法2: 手动部署

1. **克隆项目**
   ```bash
   git clone https://github.com/your-username/notion-proxy-china.git
   cd notion-proxy-china
   ```

2. **安装Vercel CLI**
   ```bash
   npm install -g vercel
   ```

3. **登录Vercel**
   ```bash
   vercel login
   ```

4. **部署项目**
   ```bash
   vercel --prod
   ```

## 📖 使用方法

部署完成后，将 Notion 图片 URL 中的域名替换为你的 Vercel 域名：

**原始URL:**
```
https://www.notion.so/image/attachment%3Ad7744410-2387-4a4f-ae2b-3b0df74c0fe3%3A21grog.png?table=block&id=222a5a8a-afda-800b-8f17-c9ddd9538bed
```

**代理URL:**
```
https://your-domain.vercel.app/api/proxy/image/attachment%3Ad7744410-2387-4a4f-ae2b-3b0df74c0fe3%3A21grog.png?table=block&id=222a5a8a-afda-800b-8f17-c9ddd9538bed
```

## 🔧 配置NotionNext

在你的 NotionNext 项目中修改 `conf/image.config.js`:

```javascript
module.exports = {
  NOTION_HOST: 'https://your-domain.vercel.app/api/proxy',
  NOTION_HOST_BACKUP: [
    'https://your-domain.vercel.app/api/proxy',
    'https://www.notion.so'
  ]
}
```

## 📊 性能对比

| 指标 | 原始访问 | 代理访问 | 提升 |
|------|----------|----------|------|
| 首次加载 | 8-15秒 | 2-4秒 | 70%+ |
| 缓存命中 | 无 | <100ms | 99%+ |
| 成功率 | 30-50% | 95%+ | 90%+ |

## 🌍 支持的服务

- ✅ Notion 图片 (`www.notion.so`)
- ✅ Notion 文件 (`file.notion.so`)
- ✅ AWS S3 图片 (`prod-files-secure.s3.us-west-2.amazonaws.com`)
- ✅ Unsplash 图片 (`images.unsplash.com`)

## 🛠️ 本地开发

```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 访问 http://localhost:3000/api/proxy
```

## 📝 许可证

MIT License - 详见 [LICENSE](LICENSE) 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请提交 [Issue](https://github.com/your-username/notion-proxy-china/issues)
