/**
 * Vercel Node.js 版本的 Notion 图片代理
 * 更兼容的部署方案
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// 配置区
const NOTION_API_HOST = 'https://www.notion.so';
const IMAGE_SITES = {
  'notion-img': 'https://img.notionusercontent.com',
  'notion-file': 'https://file.notion.so',
  'notion-static': 'https://secure.notion-static.com',
  'aws-s3': 'https://prod-files-secure.s3.us-west-2.amazonaws.com',
  's3-static': 'https://s3-us-west-2.amazonaws.com'
};

// 主处理函数
module.exports = async (req, res) => {
  // 添加调试日志
  console.log('=== 代理请求调试信息 ===');
  console.log('原始URL:', req.url);
  console.log('请求方法:', req.method);
  console.log('Host:', req.headers.host);

  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // 处理OPTIONS请求
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  const host = req.headers.host || 'localhost:3001';
  const url = new URL(req.url, `http://${host}`);
  let pathname = url.pathname;

  // 移除 /api/proxy-node 前缀（如果存在）
  if (pathname.startsWith('/api/proxy-node')) {
    pathname = pathname.replace('/api/proxy-node', '');
  }

  const prefix = pathname.split('/')[1];

  console.log('原始pathname:', url.pathname);
  console.log('处理后的pathname:', pathname);
  console.log('提取的prefix:', prefix);
  console.log('完整search参数:', url.search);

  // 处理根路径
  if (!pathname || pathname === '/') {
    res.setHeader('Content-Type', 'text/html; charset=utf-8');
    res.status(200).send(getProxyLandingPage());
    return;
  }

  try {
    // 路由 1: 处理 /image/ 路径 - 代理到 Notion
    if (pathname.startsWith('/image/')) {
      const targetUrl = 'https://www.notion.so' + pathname + url.search;
      console.log('匹配到/image/路径，目标URL:', targetUrl);
      return await proxyRequest(req, res, targetUrl, true);
    }

    // 路由 2: 图片、文件代理
    if (IMAGE_SITES[prefix]) {
      const targetDomain = IMAGE_SITES[prefix];
      const targetPath = pathname.substring(prefix.length + 2);
      const targetUrl = targetDomain + '/' + targetPath + url.search;
      return await proxyRequest(req, res, targetUrl, true);
    }

    // 路由 3: AWS S3 图片特殊处理
    if (pathname.startsWith('/https%3A%2F%2Fprod-files-secure.s3.us-west-2.amazonaws.com') ||
        pathname.startsWith('/https%3A%2F%2Fs3-us-west-2.amazonaws.com')) {
      const decodedPath = decodeURIComponent(pathname.substring(1));
      return await proxyRequest(req, res, decodedPath, true);
    }

    // 路由 4: 直接URL代理
    if (pathname.startsWith('/https%3A%2F%2F') || pathname.startsWith('/http%3A%2F%2F')) {
      const decodedPath = decodeURIComponent(pathname.substring(1));
      return await proxyRequest(req, res, decodedPath, true);
    }

    // 兜底路由: 代理到 Notion
    const defaultTargetUrl = NOTION_API_HOST + pathname + url.search;
    console.log('使用兜底路由，目标URL:', defaultTargetUrl);
    return await proxyRequest(req, res, defaultTargetUrl, false);

  } catch (error) {
    console.error('=== 代理错误 ===');
    console.error('错误信息:', error.message);
    console.error('错误堆栈:', error.stack);
    res.status(502).json({
      error: '代理请求失败',
      message: error.message,
      pathname: pathname,
      originalUrl: req.url
    });
  }
};

// 代理请求函数
async function proxyRequest(req, res, targetUrl, isImage = false) {
  return new Promise((resolve, reject) => {
    const targetUrlObj = new URL(targetUrl);
    const isHttps = targetUrlObj.protocol === 'https:';
    const httpModule = isHttps ? https : http;

    const options = {
      hostname: targetUrlObj.hostname,
      port: targetUrlObj.port || (isHttps ? 443 : 80),
      path: targetUrlObj.pathname + targetUrlObj.search,
      method: req.method,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': isImage ? 'image/webp,image/avif,image/apng,image/*,*/*;q=0.8' : req.headers.accept || '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Referer': 'https://www.notion.so/',
        'Cache-Control': 'no-cache'
      }
    };

    // 移除可能导致问题的头部
    delete options.headers.host;
    delete options.headers.connection;

    const proxyReq = httpModule.request(options, (proxyRes) => {
      // 设置响应头
      res.status(proxyRes.statusCode);
      
      // 复制响应头
      Object.keys(proxyRes.headers).forEach(key => {
        if (key.toLowerCase() !== 'content-encoding' && 
            key.toLowerCase() !== 'content-security-policy') {
          res.setHeader(key, proxyRes.headers[key]);
        }
      });

      // 设置缓存头
      if (isImage) {
        res.setHeader('Cache-Control', 'public, max-age=2592000'); // 30天
      }
      res.setHeader('Access-Control-Allow-Origin', '*');

      // 管道响应
      proxyRes.pipe(res);
      
      proxyRes.on('end', () => {
        resolve();
      });
    });

    proxyReq.on('error', (error) => {
      console.error('Proxy request error:', error);
      if (!res.headersSent) {
        if (isImage) {
          // 返回占位图片
          const placeholderImage = Buffer.from([
            0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
            0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
            0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
            0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
            0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
            0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
          ]);
          res.setHeader('Content-Type', 'image/png');
          res.status(200).send(placeholderImage);
        } else {
          res.status(502).json({ error: '代理请求失败', message: error.message });
        }
      }
      reject(error);
    });

    // 如果有请求体，转发它
    if (req.body) {
      const bodyData = typeof req.body === 'string' ? req.body : JSON.stringify(req.body);
      proxyReq.write(bodyData);
    }
    
    proxyReq.end();
  });
}

// 门户页面
function getProxyLandingPage() {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notion 图片代理服务</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        h1 { color: #333; margin-bottom: 10px; }
        .subtitle { color: #666; margin-bottom: 30px; }
        .usage { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #007bff; }
        .example { background: #e9ecef; padding: 15px; border-radius: 6px; margin: 10px 0; }
        code { background: #e9ecef; padding: 2px 6px; border-radius: 4px; font-family: 'Courier New', monospace; }
        .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .feature { background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center; }
        .status { background: #d4edda; color: #155724; padding: 10px; border-radius: 6px; margin: 20px 0; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Notion 图片代理服务</h1>
        <p class="subtitle">专为中国内地用户优化的 Notion 图片加速服务</p>
        
        <div class="status">
            ✅ 服务运行正常 | 🚀 中国内地优化 | 🆓 完全免费
        </div>
        
        <div class="usage">
            <h3>📖 使用方法:</h3>
            <p>将 Notion 图片 URL 中的域名替换为本服务域名</p>
            
            <div class="example">
                <strong>原始URL:</strong><br>
                <code>https://www.notion.so/image/xxx</code>
            </div>
            
            <div class="example">
                <strong>代理URL:</strong><br>
                <code>https://your-domain.vercel.app/api/proxy-node/image/xxx</code>
            </div>
        </div>
        
        <div class="features">
            <div class="feature">
                <strong>🎯 智能优化</strong><br>
                自动WebP格式转换
            </div>
            <div class="feature">
                <strong>⚡ 极速缓存</strong><br>
                30天边缘缓存
            </div>
            <div class="feature">
                <strong>🌏 中国友好</strong><br>
                亚洲节点加速
            </div>
        </div>
        
        <p style="text-align: center; color: #666; margin-top: 30px;">
            Powered by Vercel Node.js Runtime | 为NotionNext优化
        </p>
    </div>
</body>
</html>`;
}
