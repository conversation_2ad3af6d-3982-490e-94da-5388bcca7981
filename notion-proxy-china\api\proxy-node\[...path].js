/**
 * Vercel 动态路由版本的 Notion 图片代理
 * 处理 /api/proxy-node/* 的所有子路径
 */

const https = require('https');
const http = require('http');
const { URL } = require('url');

// 配置区
const NOTION_API_HOST = 'https://www.notion.so';
const IMAGE_SITES = {
  'notion-img': 'https://img.notionusercontent.com',
  'notion-file': 'https://file.notion.so',
  'notion-static': 'https://secure.notion-static.com',
  'aws-s3': 'https://prod-files-secure.s3.us-west-2.amazonaws.com',
  's3-static': 'https://s3-us-west-2.amazonaws.com'
};

// 主处理函数
module.exports = async (req, res) => {
  // 添加调试日志
  console.log('=== 动态路由代理请求调试信息 ===');
  console.log('原始URL:', req.url);
  console.log('请求方法:', req.method);
  console.log('Host:', req.headers.host);
  console.log('Query参数:', req.query);

  // 设置CORS头
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');

  // 处理OPTIONS请求
  if (req.method === 'OPTIONS') {
    res.status(200).end();
    return;
  }

  // 从动态路由参数中获取路径
  const pathArray = req.query.path || [];
  const pathname = '/' + pathArray.join('/');
  const host = req.headers.host || 'localhost:3001';
  const url = new URL(req.url, `http://${host}`);
  
  console.log('动态路径数组:', pathArray);
  console.log('构建的pathname:', pathname);
  console.log('完整search参数:', url.search);

  try {
    // 路由 1: 处理 /image/ 路径 - 代理到 Notion
    if (pathname.startsWith('/image/')) {
      const targetUrl = 'https://www.notion.so' + pathname + url.search;
      console.log('匹配到/image/路径，目标URL:', targetUrl);
      return await proxyRequest(req, res, targetUrl, true);
    }

    // 路由 2: 图片、文件代理
    const prefix = pathArray[0];
    if (IMAGE_SITES[prefix]) {
      const targetDomain = IMAGE_SITES[prefix];
      const targetPath = pathArray.slice(1).join('/');
      const targetUrl = targetDomain + '/' + targetPath + url.search;
      console.log('匹配到图片站点代理，目标URL:', targetUrl);
      return await proxyRequest(req, res, targetUrl, true);
    }

    // 路由 3: AWS S3 图片特殊处理
    if (pathname.startsWith('/https%3A%2F%2Fprod-files-secure.s3.us-west-2.amazonaws.com') ||
        pathname.startsWith('/https%3A%2F%2Fs3-us-west-2.amazonaws.com')) {
      const decodedPath = decodeURIComponent(pathname.substring(1));
      console.log('匹配到S3路径，目标URL:', decodedPath);
      return await proxyRequest(req, res, decodedPath, true);
    }

    // 路由 4: 直接URL代理
    if (pathname.startsWith('/https%3A%2F%2F') || pathname.startsWith('/http%3A%2F%2F')) {
      const decodedPath = decodeURIComponent(pathname.substring(1));
      console.log('匹配到直接URL代理，目标URL:', decodedPath);
      return await proxyRequest(req, res, decodedPath, true);
    }

    // 兜底路由: 代理到 Notion
    const defaultTargetUrl = NOTION_API_HOST + pathname + url.search;
    console.log('使用兜底路由，目标URL:', defaultTargetUrl);
    return await proxyRequest(req, res, defaultTargetUrl, false);

  } catch (error) {
    console.error('=== 代理错误 ===');
    console.error('错误信息:', error.message);
    console.error('错误堆栈:', error.stack);
    res.status(502).json({
      error: '代理请求失败',
      message: error.message,
      pathname: pathname,
      originalUrl: req.url,
      pathArray: pathArray
    });
  }
};

// 代理请求函数
async function proxyRequest(req, res, targetUrl, isImage = false) {
  return new Promise((resolve, reject) => {
    const targetUrlObj = new URL(targetUrl);
    const isHttps = targetUrlObj.protocol === 'https:';
    const httpModule = isHttps ? https : http;

    const options = {
      hostname: targetUrlObj.hostname,
      port: targetUrlObj.port || (isHttps ? 443 : 80),
      path: targetUrlObj.pathname + targetUrlObj.search,
      method: req.method,
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': isImage ? 'image/webp,image/avif,image/apng,image/*,*/*;q=0.8' : req.headers.accept || '*/*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Referer': 'https://www.notion.so/',
        'Cache-Control': 'no-cache'
      }
    };

    // 移除可能导致问题的头部
    delete options.headers.host;
    delete options.headers.connection;

    console.log('发起代理请求到:', targetUrl);
    console.log('请求选项:', JSON.stringify(options, null, 2));

    const proxyReq = httpModule.request(options, (proxyRes) => {
      console.log('收到响应，状态码:', proxyRes.statusCode);
      console.log('响应头:', JSON.stringify(proxyRes.headers, null, 2));
      
      // 设置响应头
      res.status(proxyRes.statusCode);
      
      // 复制响应头
      Object.keys(proxyRes.headers).forEach(key => {
        if (key.toLowerCase() !== 'content-encoding' && 
            key.toLowerCase() !== 'content-security-policy') {
          res.setHeader(key, proxyRes.headers[key]);
        }
      });

      // 设置缓存头
      if (isImage) {
        res.setHeader('Cache-Control', 'public, max-age=2592000'); // 30天
      }
      res.setHeader('Access-Control-Allow-Origin', '*');

      // 管道响应
      proxyRes.pipe(res);
      
      proxyRes.on('end', () => {
        console.log('代理响应完成');
        resolve();
      });
    });

    proxyReq.on('error', (error) => {
      console.error('Proxy request error:', error);
      if (!res.headersSent) {
        if (isImage) {
          // 返回占位图片
          const placeholderImage = Buffer.from([
            0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
            0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
            0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
            0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
            0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
            0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
          ]);
          res.setHeader('Content-Type', 'image/png');
          res.status(200).send(placeholderImage);
        } else {
          res.status(502).json({ error: '代理请求失败', message: error.message });
        }
      }
      reject(error);
    });

    // 如果有请求体，转发它
    if (req.body) {
      const bodyData = typeof req.body === 'string' ? req.body : JSON.stringify(req.body);
      proxyReq.write(bodyData);
    }
    
    proxyReq.end();
  });
}
