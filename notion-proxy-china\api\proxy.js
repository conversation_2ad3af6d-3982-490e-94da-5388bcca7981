/**
 * Vercel Edge Functions 版本的 Notion 图片代理
 * 针对中国内地优化的免费部署方案
 */

// 配置区
const NOTION_API_HOST = 'https://www.notion.so';

const IMAGE_SITES = {
  'notion-img': 'https://img.notionusercontent.com',
  'notion-file': 'https://file.notion.so',
  'notion-static': 'https://secure.notion-static.com',
  'aws-s3': 'https://prod-files-secure.s3.us-west-2.amazonaws.com',
  's3-static': 'https://s3-us-west-2.amazonaws.com'
};

const RATE_LIMIT_CONFIG = {
  maxRetries: 5,
  baseDelay: 500,
  maxDelay: 5000,
  backoffMultiplier: 1.5,
  timeout: 15000
};

export const config = {
  runtime: 'edge',
};

export default async function handler(request) {
  const url = new URL(request.url);
  const pathname = url.pathname.replace('/api/proxy', '');
  const prefix = pathname.split('/')[1];

  // 处理根路径
  if (!pathname || pathname === '/') {
    return new Response(getProxyLandingPage(), {
      status: 200,
      headers: { 'Content-Type': 'text/html; charset=utf-8' }
    });
  }

  // 路由 1: 图片、文件代理
  if (IMAGE_SITES[prefix]) {
    const targetDomain = IMAGE_SITES[prefix];
    const targetPath = pathname.substring(prefix.length + 2);
    const targetUrl = new URL(targetDomain + '/' + targetPath + url.search);
    return proxyImageWithCache(request, targetUrl);
  }

  // 路由 2: AWS S3 图片特殊处理
  if (pathname.startsWith('/https%3A%2F%2Fprod-files-secure.s3.us-west-2.amazonaws.com') ||
      pathname.startsWith('/https%3A%2F%2Fs3-us-west-2.amazonaws.com')) {
    const decodedPath = decodeURIComponent(pathname.substring(1));
    const targetUrl = new URL(decodedPath);
    return proxyImageWithCache(request, targetUrl);
  }

  // 路由 3: 直接URL代理
  if (pathname.startsWith('/https%3A%2F%2F') || pathname.startsWith('/http%3A%2F%2F')) {
    const decodedPath = decodeURIComponent(pathname.substring(1));
    const targetUrl = new URL(decodedPath);
    return proxyImageWithCache(request, targetUrl);
  }

  // 兜底路由: 代理到 Notion
  const defaultTargetUrl = new URL(NOTION_API_HOST + pathname + url.search);
  return proxyRequest(request, defaultTargetUrl);
}

// 图片代理函数
async function proxyImageWithCache(request, targetUrl) {
  const optimizedUrl = new URL(targetUrl);
  const acceptHeader = request.headers.get('Accept') || '';

  // WebP优化
  if (acceptHeader.includes('image/webp') || acceptHeader.includes('image/*')) {
    optimizedUrl.searchParams.set('format', 'webp');
  }

  // 添加优化参数
  addOptimizedImageParams(optimizedUrl, request.headers);

  // 创建优化的请求
  const modifiedRequest = new Request(optimizedUrl, {
    method: request.method,
    headers: {
      'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
      'Referer': 'https://www.notion.so/',
      'Accept': 'image/webp,image/avif,image/apng,image/*,*/*;q=0.8',
      'Accept-Encoding': 'gzip, deflate, br',
      'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
      'Cache-Control': 'no-cache',
      'Sec-Fetch-Dest': 'image',
      'Sec-Fetch-Mode': 'no-cors',
      'Sec-Fetch-Site': 'cross-site'
    }
  });

  try {
    const response = await fetch(modifiedRequest);
    
    if (response.ok) {
      const newResponse = new Response(response.body, response);
      newResponse.headers.set('Cache-Control', 'public, max-age=2592000'); // 30天缓存
      newResponse.headers.set('Access-Control-Allow-Origin', '*');
      return newResponse;
    } else if (response.status === 429) {
      return createPlaceholderImageResponse();
    }
    
    return response;
  } catch (error) {
    console.error('Proxy error:', error);
    return createPlaceholderImageResponse();
  }
}

// 基础请求代理
async function proxyRequest(request, targetUrl, retryCount = 0) {
  try {
    const response = await fetch(targetUrl, {
      method: request.method,
      headers: request.headers,
      body: request.body
    });

    if (response.status === 429 && retryCount < RATE_LIMIT_CONFIG.maxRetries) {
      const delay = Math.min(
        RATE_LIMIT_CONFIG.baseDelay * Math.pow(RATE_LIMIT_CONFIG.backoffMultiplier, retryCount),
        RATE_LIMIT_CONFIG.maxDelay
      );
      await new Promise(resolve => setTimeout(resolve, delay));
      return proxyRequest(request, targetUrl, retryCount + 1);
    }

    const modifiedResponse = new Response(response.body, response);
    modifiedResponse.headers.set('Access-Control-Allow-Origin', '*');
    modifiedResponse.headers.delete('Content-Security-Policy');
    return modifiedResponse;
  } catch (error) {
    return new Response(`代理请求失败: ${error.message}`, { status: 502 });
  }
}

// 图片参数优化
function addOptimizedImageParams(targetUrl, requestHeaders) {
  const optimizableDomains = [
    'img.notionusercontent.com',
    'file.notion.so',
    'images.unsplash.com',
    'prod-files-secure.s3.us-west-2.amazonaws.com',
    's3-us-west-2.amazonaws.com'
  ];

  if (optimizableDomains.includes(targetUrl.hostname)) {
    const userAgent = requestHeaders.get('User-Agent') || '';
    let width;
    if (/iPhone|Android.*Mobile/.test(userAgent)) width = 750;
    else if (/iPad|Tablet/.test(userAgent)) width = 1200;
    else width = 1920;

    if (targetUrl.hostname.includes('amazonaws.com')) {
      if (width) {
        targetUrl.searchParams.set('response-cache-control', 'max-age=31536000');
      }
    } else if (width) {
      targetUrl.searchParams.set('width', width);
    }
  }
}

// 占位图片
function createPlaceholderImageResponse() {
  const placeholderImage = new Uint8Array([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
    0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
    0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
    0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
    0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
    0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
  ]);

  return new Response(placeholderImage, {
    status: 200,
    headers: {
      'Content-Type': 'image/png',
      'Cache-Control': 'public, max-age=3600',
      'Access-Control-Allow-Origin': '*'
    }
  });
}

// 门户页面
function getProxyLandingPage() {
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Notion 图片代理服务</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
        .container { background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
        h1 { color: #333; margin-bottom: 10px; }
        .subtitle { color: #666; margin-bottom: 30px; }
        .usage { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #007bff; }
        .example { background: #e9ecef; padding: 15px; border-radius: 6px; margin: 10px 0; }
        code { background: #e9ecef; padding: 2px 6px; border-radius: 4px; font-family: 'Courier New', monospace; }
        .features { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 20px 0; }
        .feature { background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center; }
        .status { background: #d4edda; color: #155724; padding: 10px; border-radius: 6px; margin: 20px 0; text-align: center; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🖼️ Notion 图片代理服务</h1>
        <p class="subtitle">专为中国内地用户优化的 Notion 图片加速服务</p>
        
        <div class="status">
            ✅ 服务运行正常 | 🚀 中国内地优化 | 🆓 完全免费
        </div>
        
        <div class="usage">
            <h3>📖 使用方法:</h3>
            <p>将 Notion 图片 URL 中的域名替换为本服务域名</p>
            
            <div class="example">
                <strong>原始URL:</strong><br>
                <code>https://www.notion.so/image/xxx</code>
            </div>
            
            <div class="example">
                <strong>代理URL:</strong><br>
                <code>https://your-domain.vercel.app/api/proxy/image/xxx</code>
            </div>
        </div>
        
        <div class="features">
            <div class="feature">
                <strong>🎯 智能优化</strong><br>
                自动WebP格式转换
            </div>
            <div class="feature">
                <strong>⚡ 极速缓存</strong><br>
                30天边缘缓存
            </div>
            <div class="feature">
                <strong>🌏 中国友好</strong><br>
                亚洲节点加速
            </div>
        </div>
        
        <p style="text-align: center; color: #666; margin-top: 30px;">
            Powered by Vercel Edge Functions | 为NotionNext优化
        </p>
    </div>
</body>
</html>`;
}
