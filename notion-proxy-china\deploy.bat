@echo off
chcp 65001 >nul
echo.
echo ========================================
echo 🚀 Notion代理服务 - 一键部署脚本
echo ========================================
echo.

:menu
echo 请选择部署平台:
echo.
echo [1] Vercel (推荐 - 中国内地最快)
echo [2] Railway (备选 - 简单部署)
echo [3] 本地测试
echo [4] 退出
echo.
set /p choice=请输入选择 (1-4): 

if "%choice%"=="1" goto vercel
if "%choice%"=="2" goto railway
if "%choice%"=="3" goto local
if "%choice%"=="4" goto exit
echo 无效选择，请重新输入
goto menu

:vercel
echo.
echo 🔵 准备部署到 Vercel...
echo.
echo 步骤1: 检查依赖
where git >nul 2>nul
if errorlevel 1 (
    echo ❌ 未找到 Git，请先安装 Git
    pause
    goto menu
)

where npm >nul 2>nul
if errorlevel 1 (
    echo ❌ 未找到 npm，请先安装 Node.js
    pause
    goto menu
)

echo ✅ 依赖检查完成

echo.
echo 步骤2: 安装 Vercel CLI
npm install -g vercel

echo.
echo 步骤3: 初始化 Git 仓库
git init
git add .
git commit -m "Initial commit - Notion Proxy China"

echo.
echo 步骤4: 部署到 Vercel
echo 💡 提示: 首次使用需要登录 Vercel 账号
vercel --prod

echo.
echo ✅ Vercel 部署完成！
echo 📝 请记录部署后的域名，用于配置 NotionNext
pause
goto menu

:railway
echo.
echo 🟣 准备部署到 Railway...
echo.
echo 步骤1: 安装依赖
npm install

echo.
echo 步骤2: 本地测试
echo 💡 测试完成后按 Ctrl+C 停止，然后访问 railway.app 手动部署
npm run railway:dev

pause
goto menu

:local
echo.
echo 🟡 启动本地测试服务器...
echo.
echo 选择测试模式:
echo [1] Vercel 模式 (Edge Functions)
echo [2] Railway 模式 (Express Server)
set /p test_choice=请选择 (1-2): 

if "%test_choice%"=="1" (
    echo 启动 Vercel 开发服务器...
    npm install -g vercel
    vercel dev
) else if "%test_choice%"=="2" (
    echo 启动 Express 服务器...
    npm install
    npm run railway:dev
) else (
    echo 无效选择
    goto local
)

pause
goto menu

:exit
echo.
echo 👋 感谢使用！
echo.
pause
exit

:error
echo.
echo ❌ 部署过程中出现错误
echo 💡 请检查网络连接和依赖安装
pause
goto menu
