/**
 * 本地开发服务器
 * 模拟Vercel的API路由行为
 */

const express = require('express');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3000;

// 中间件
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 导入API函数
const proxyNodeHandler = require('./api/proxy-node.js');
const proxyNodeDynamicHandler = require('./api/proxy-node/[...path].js');

// 模拟Vercel的req/res对象
function createVercelRequest(req) {
  // 解析动态路径参数
  const pathMatch = req.path.match(/^\/api\/proxy-node\/(.*)$/);
  const query = { ...req.query };
  
  if (pathMatch && pathMatch[1]) {
    // 为动态路由设置path参数
    query.path = pathMatch[1].split('/').filter(Boolean);
  }
  
  return {
    ...req,
    query,
    url: req.originalUrl
  };
}

function createVercelResponse(res) {
  const originalStatus = res.status.bind(res);
  const originalSend = res.send.bind(res);
  const originalJson = res.json.bind(res);
  
  return {
    ...res,
    status: (code) => {
      res.statusCode = code;
      return {
        send: (data) => {
          res.status(code).send(data);
        },
        json: (data) => {
          res.status(code).json(data);
        },
        end: () => {
          res.status(code).end();
        }
      };
    }
  };
}

// API路由 - 根路径
app.all('/api/proxy-node', async (req, res) => {
  try {
    const vercelReq = createVercelRequest(req);
    const vercelRes = createVercelResponse(res);
    await proxyNodeHandler(vercelReq, vercelRes);
  } catch (error) {
    console.error('API处理错误:', error);
    res.status(500).json({ error: '内部服务器错误', message: error.message });
  }
});

// API路由 - 动态路径
app.all('/api/proxy-node/*', async (req, res) => {
  try {
    const vercelReq = createVercelRequest(req);
    const vercelRes = createVercelResponse(res);
    await proxyNodeDynamicHandler(vercelReq, vercelRes);
  } catch (error) {
    console.error('API处理错误:', error);
    res.status(500).json({ error: '内部服务器错误', message: error.message });
  }
});

// 重写规则 - 模拟vercel.json的rewrites
app.all('/image/*', async (req, res) => {
  // 重写到API路由
  req.url = '/api/proxy-node' + req.path + (req.url.includes('?') ? '?' + req.url.split('?')[1] : '');
  req.path = '/api/proxy-node' + req.path;
  
  try {
    const vercelReq = createVercelRequest(req);
    const vercelRes = createVercelResponse(res);
    await proxyNodeDynamicHandler(vercelReq, vercelRes);
  } catch (error) {
    console.error('重写路由处理错误:', error);
    res.status(500).json({ error: '内部服务器错误', message: error.message });
  }
});

app.all('/file/*', async (req, res) => {
  req.url = '/api/proxy-node' + req.path + (req.url.includes('?') ? '?' + req.url.split('?')[1] : '');
  req.path = '/api/proxy-node' + req.path;
  
  try {
    const vercelReq = createVercelRequest(req);
    const vercelRes = createVercelResponse(res);
    await proxyNodeDynamicHandler(vercelReq, vercelRes);
  } catch (error) {
    console.error('重写路由处理错误:', error);
    res.status(500).json({ error: '内部服务器错误', message: error.message });
  }
});

// 根路径重定向到API
app.get('/', (req, res) => {
  res.redirect('/api/proxy-node');
});

// 错误处理中间件
app.use((error, req, res, next) => {
  console.error('全局错误处理:', error);
  res.status(500).json({
    error: '服务器内部错误',
    message: error.message,
    stack: process.env.NODE_ENV === 'development' ? error.stack : undefined
  });
});

// 404处理
app.use((req, res) => {
  res.status(404).json({
    error: '路径未找到',
    path: req.path,
    message: '请检查URL是否正确'
  });
});

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 本地开发服务器启动成功!`);
  console.log(`📍 服务地址: http://localhost:${PORT}`);
  console.log(`🔗 API测试: http://localhost:${PORT}/api/proxy-node`);
  console.log(`🖼️ 图片代理测试: http://localhost:${PORT}/image/xxx`);
  console.log(`\n💡 使用方法:`);
  console.log(`   将 Notion 图片URL 中的域名替换为: http://localhost:${PORT}`);
  console.log(`   例如: http://localhost:${PORT}/image/attachment%3Ab6be2127...`);
  console.log(`\n⏹️  按 Ctrl+C 停止服务器`);
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n👋 正在关闭服务器...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n👋 正在关闭服务器...');
  process.exit(0);
});
