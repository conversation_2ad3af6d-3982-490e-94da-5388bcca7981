{"name": "@edge-runtime/format", "description": "A printf-like string formatter for Edge Runtime", "homepage": "https://github.com/vercel/edge-runtime#readme", "version": "2.2.1", "main": "dist/index.js", "repository": {"directory": "packages/format", "type": "git", "url": "git+https://github.com/vercel/edge-runtime.git"}, "bugs": {"url": "https://github.com/vercel/edge-runtime/issues"}, "keywords": ["edge", "edge-runtime", "format", "formatter", "functions", "printf", "runtime", "standard", "web"], "devDependencies": {"tsup": "8"}, "engines": {"node": ">=16"}, "files": ["dist"], "license": "MPL-2.0", "publishConfig": {"access": "public"}, "types": "dist/index.d.ts", "scripts": {"build": "tsup", "clean": "pnpm run clean:node && pnpm run clean:build", "clean:build": "rm -rf dist", "clean:node": "rm -rf node_modules", "prebuild": "pnpm run clean:build", "test": "TZ=UTC jest"}}