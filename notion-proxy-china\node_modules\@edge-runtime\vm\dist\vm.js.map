{"version": 3, "file": "vm.js", "sourceRoot": "", "sources": ["../src/vm.ts"], "names": [], "mappings": ";;;AACA,2BAAgD;AAehD;;;;GAIG;AACH,MAAa,EAAE;IAGb,YAAY,UAAwB,EAAE;;QACpC,MAAM,OAAO,GAAG,IAAA,kBAAa,EAC3B,EAAE,EACF;YACE,IAAI,EAAE,cAAc;YACpB,cAAc,EAAE,MAAA,OAAO,CAAC,cAAc,mCAAI;gBACxC,OAAO,EAAE,KAAK;gBACd,IAAI,EAAE,IAAI;aACX;SACF,CACW,CAAA;QAEd,IAAI,CAAC,OAAO,GAAG,MAAA,MAAA,OAAO,CAAC,MAAM,wDAAG,OAAO,CAAC,mCAAK,OAAyB,CAAA;IACxE,CAAC;IAED;;OAEG;IACH,QAAQ,CAAU,IAAY;QAC5B,OAAO,IAAA,iBAAY,EAAC,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,CAAA;IACzC,CAAC;CACF;AAxBD,gBAwBC", "sourcesContent": ["import type { CreateContextOptions } from 'vm'\nimport { createContext, runInContext } from 'vm'\n\nexport interface VMOptions<T> {\n  /**\n   * Provide code generation options to the Node.js VM.\n   * If you don't provide any option, code generation will be disabled.\n   */\n  codeGeneration?: CreateContextOptions['codeGeneration']\n  /**\n   * Allows to extend the VMContext. Note that it must return a contextified\n   * object so ideally it should return the same reference it receives.\n   */\n  extend?: (context: VMContext) => VMContext & T\n}\n\n/**\n * A raw VM with a context that can be extended on instantiation. Implements\n * a realm-like interface where one can evaluate code or require CommonJS\n * modules in multiple ways.\n */\nexport class VM<T extends Record<string | number, any>> {\n  public readonly context: VMContext & T\n\n  constructor(options: VMOptions<T> = {}) {\n    const context = createContext(\n      {},\n      {\n        name: 'Edge Runtime',\n        codeGeneration: options.codeGeneration ?? {\n          strings: false,\n          wasm: true,\n        },\n      },\n    ) as VMContext\n\n    this.context = options.extend?.(context) ?? (context as VMContext & T)\n  }\n\n  /**\n   * Allows to run arbitrary code within the VM.\n   */\n  evaluate<T = any>(code: string): T {\n    return runInContext(code, this.context)\n  }\n}\n\nexport interface VMContext {\n  Array: typeof Array\n  ArrayBuffer: typeof ArrayBuffer\n  Atomics: typeof Atomics\n  BigInt: typeof BigInt\n  BigInt64Array: typeof BigInt64Array\n  BigUint64Array: typeof BigUint64Array\n  Boolean: typeof Boolean\n  DataView: typeof DataView\n  Date: typeof Date\n  decodeURI: typeof decodeURI\n  decodeURIComponent: typeof decodeURIComponent\n  encodeURI: typeof encodeURI\n  encodeURIComponent: typeof encodeURIComponent\n  Error: typeof Error\n  EvalError: typeof EvalError\n  Float32Array: typeof Float32Array\n  Float64Array: typeof Float64Array\n  Function: typeof Function\n  Infinity: typeof Infinity\n  Int8Array: typeof Int8Array\n  Int16Array: typeof Int16Array\n  Int32Array: typeof Int32Array\n  Intl: typeof Intl\n  isFinite: typeof isFinite\n  isNaN: typeof isNaN\n  JSON: typeof JSON\n  Map: typeof Map\n  Math: typeof Math\n  Number: typeof Number\n  Object: typeof Object\n  parseFloat: typeof parseFloat\n  parseInt: typeof parseInt\n  Promise: typeof Promise\n  Proxy: typeof Proxy\n  RangeError: typeof RangeError\n  ReferenceError: typeof ReferenceError\n  Reflect: typeof Reflect\n  RegExp: typeof RegExp\n  Set: typeof Set\n  SharedArrayBuffer: typeof SharedArrayBuffer\n  String: typeof String\n  Symbol: typeof Symbol\n  SyntaxError: typeof SyntaxError\n  TypeError: typeof TypeError\n  Uint8Array: typeof Uint8Array\n  Uint8ClampedArray: typeof Uint8ClampedArray\n  Uint16Array: typeof Uint16Array\n  Uint32Array: typeof Uint32Array\n  URIError: typeof URIError\n  WeakMap: typeof WeakMap\n  WeakSet: typeof WeakSet\n  WebAssembly: typeof WebAssembly\n  [key: string | number]: any\n}\n"]}