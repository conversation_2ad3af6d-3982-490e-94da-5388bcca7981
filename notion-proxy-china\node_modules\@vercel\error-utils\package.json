{"name": "@vercel/error-utils", "version": "2.0.3", "description": "A collection of error utilities for vercel/vercel", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "repository": {"type": "git", "url": "https://github.com/vercel/vercel.git", "directory": "packages/error-utils"}, "license": "Apache-2.0", "devDependencies": {"@types/jest": "29.2.1", "@types/node": "14.18.33", "jest-junit": "16.0.0", "typescript": "4.9.5", "vitest": "2.1.4"}, "scripts": {"build": "node ../../utils/build.mjs", "test": "jest --reporters=default --reporters=jest-junit --coverage --env node --verbose", "vitest-run": "vitest -c ../../vitest.config.mts", "vitest-unit": "glob --absolute 'test/*.test.ts'", "type-check": "tsc --noEmit"}}