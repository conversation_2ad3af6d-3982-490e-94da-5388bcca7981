{"version": 3, "file": "create-handler.js", "sourceRoot": "", "sources": ["../../src/server/create-handler.ts"], "names": [], "mappings": ";;;;;;AAIA,iDAAgF;AAChF,0DAAgC;AAChC,0DAAgC;AAEhC,+BAAmC;AAcnC;;;;;GAKG;AACH,SAAgB,aAAa,CAAwB,OAAmB;IACtE,MAAM,QAAQ,GAA0B,IAAI,GAAG,EAAE,CAAA;IAEjD,OAAO;QACL,OAAO,EAAE,KAAK,EAAE,GAAoB,EAAE,GAAmB,EAAE,EAAE;;YAC3D,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,IAAA,mBAAQ,GAAE,CAAA;gBAExB,MAAM,IAAI,GACR,GAAG,CAAC,MAAM,KAAK,KAAK,IAAI,GAAG,CAAC,MAAM,KAAK,MAAM;oBAC3C,CAAC,CAAC,IAAA,oCAAqB,EACnB,GAAG,EACH,OAAO,CAAC,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC,EACtC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC,eAAe,CACxC;oBACH,CAAC,CAAC,SAAS,CAAA;gBAEf,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,OAAO,CAAC,aAAa,CAClD,MAAM,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,EACnB;oBACE,OAAO,EAAE,oBAAoB,CAAC,GAAG,CAAC;oBAClC,MAAM,EAAE,GAAG,CAAC,MAAM;oBAClB,IAAI,EAAE,IAAI,aAAJ,IAAI,uBAAJ,IAAI,CAAE,eAAe,EAAE;iBAC9B,CACF,CAAA;gBAED,MAAM,SAAS,GAAG,QAAQ,CAAC,SAAS,EAAE,CAAA;gBACtC,QAAQ,CAAC,GAAG,CAAC,SAAS,CAAC,CAAA;gBACvB,SAAS,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,CAAA;gBAEnD,GAAG,CAAC,UAAU,GAAG,QAAQ,CAAC,MAAM,CAAA;gBAChC,GAAG,CAAC,aAAa,GAAG,QAAQ,CAAC,UAAU,CAAA;gBAEvC,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CACvC,aAAa,CAAC,QAAQ,CAAC,OAAO,CAAC,CAChC,EAAE,CAAC;oBACF,IAAI,KAAK,KAAK,SAAS,EAAE,CAAC;wBACxB,GAAG,CAAC,SAAS,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;oBAC3B,CAAC;gBACH,CAAC;gBAED,MAAM,IAAA,uCAAwB,EAAC,QAAQ,CAAC,IAAI,EAAE,GAAG,CAAC,CAAA;gBAElD,MAAM,OAAO,GAAG,GAAG,GAAG,CAAC,MAAM,CAAC,aAAa,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,GAAG,EAAE,CAAA;gBACtE,MAAM,IAAI,GAAG,GAAG,MAAA,IAAA,mBAAQ,EAAC,KAAK,EAAE,CAAC;qBAC9B,KAAK,CAAC,mBAAmB,CAAC,0CACzB,IAAI,CAAC,GAAG,CAAC,EAAE,CAAA;gBAEf,MAAM,IAAI,GAAG,GAAG,GAAG,CAAC,UAAU,IAAI,mBAAY,CAAC,GAAG,CAAC,UAAU,CAAC,EAAE,CAAA;gBAChE,MAAA,OAAO,CAAC,MAAM,0CAAE,KAAK,CAAC,GAAG,OAAO,MAAM,IAAI,OAAO,IAAI,EAAE,CAAC,CAAA;gBACxD,GAAG,CAAC,GAAG,EAAE,CAAA;YACX,CAAC;oBAAS,CAAC;gBACT,IAAI,CAAC,GAAG,CAAC,aAAa,EAAE,CAAC;oBACvB,GAAG,CAAC,GAAG,EAAE,CAAA;gBACX,CAAC;YACH,CAAC;QACH,CAAC;QAED,SAAS,EAAE,GAAG,EAAE,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC;KACvC,CAAA;AACH,CAAC;AA5DD,sCA4DC;AAED;;;;GAIG;AACH,SAAS,MAAM,CAAC,GAAoB;;IAClC,MAAM,KAAK,GAAG,CAAA,MAAC,GAAG,CAAC,MAAc,0CAAE,SAAS,EAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAA;IAC/D,OAAO,IAAI,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,GAAG,KAAK,MAAM,MAAM,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE,CAAC,CAAA;AAC3E,CAAC;AAED;;;;GAIG;AACH,SAAS,oBAAoB,CAAC,GAAoB;IAChD,OAAO,MAAM,CAAC,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,EAAE;QAC1C,MAAM,KAAK,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA;QAC9B,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,KAAK,aAAL,KAAK,cAAL,KAAK,GAAI,EAAE,CAAC,CAAA;IACrE,CAAC,CAAC,CAAA;AACJ,CAAC;AAED;;;GAGG;AACH,SAAS,aAAa,CAAC,OAAiB;IACtC,MAAM,MAAM,GAAgB,EAAE,CAAA;IAC9B,IAAI,OAAO,EAAE,CAAC;QACZ,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,OAAO,CAAC,OAAO,EAAE,EAAE,CAAC;YAC7C,MAAM,CAAC,GAAG,CAAC,GAAG,GAAG,KAAK,YAAY,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,CAAC,CAAC,CAAC,KAAK,CAAA;QACrE,CAAC;IACH,CAAC;IACD,OAAO,MAAM,CAAA;AACf,CAAC", "sourcesContent": ["import type { EdgeRuntime } from '../edge-runtime'\nimport type { IncomingMessage, ServerResponse } from 'http'\nimport type { Logger, NodeHeaders } from '../types'\nimport type { EdgeContext } from '@edge-runtime/vm'\nimport { getClonableBodyStream, pipeBodyStreamToResponse } from './body-streams'\nimport prettyMs from 'pretty-ms'\nimport timeSpan from 'time-span'\n\nimport { STATUS_CODES } from 'http'\n\nexport interface Options<T extends EdgeContext> {\n  /**\n   * A logger interface. If none is provided there will be no logs.\n   */\n  logger?: Logger\n  /**\n   * The runtime where the FetchEvent will be triggered whenever the server\n   * receives a request.\n   */\n  runtime: EdgeRuntime<T>\n}\n\n/**\n * Creates an HHTP handler that can be used to create a Node.js HTTP server.\n * Whenever a request is handled it will transform it into a `dispatchFetch`\n * call for the given `EdgeRuntime`. Then it will transform the response\n * into an HTTP response.\n */\nexport function createHandler<T extends EdgeContext>(options: Options<T>) {\n  const awaiting: Set<Promise<unknown>> = new Set()\n\n  return {\n    handler: async (req: IncomingMessage, res: ServerResponse) => {\n      try {\n        const start = timeSpan()\n\n        const body =\n          req.method !== 'GET' && req.method !== 'HEAD'\n            ? getClonableBodyStream(\n                req,\n                options.runtime.evaluate('Uint8Array'),\n                options.runtime.context.TransformStream,\n              )\n            : undefined\n\n        const response = await options.runtime.dispatchFetch(\n          String(getURL(req)),\n          {\n            headers: toRequestInitHeaders(req),\n            method: req.method,\n            body: body?.cloneBodyStream(),\n          },\n        )\n\n        const waitUntil = response.waitUntil()\n        awaiting.add(waitUntil)\n        waitUntil.finally(() => awaiting.delete(waitUntil))\n\n        res.statusCode = response.status\n        res.statusMessage = response.statusText\n\n        for (const [key, value] of Object.entries(\n          toNodeHeaders(response.headers),\n        )) {\n          if (value !== undefined) {\n            res.setHeader(key, value)\n          }\n        }\n\n        await pipeBodyStreamToResponse(response.body, res)\n\n        const subject = `${req.socket.remoteAddress} ${req.method} ${req.url}`\n        const time = `${prettyMs(start())\n          .match(/[a-zA-Z]+|[0-9]+/g)\n          ?.join(' ')}`\n\n        const code = `${res.statusCode} ${STATUS_CODES[res.statusCode]}`\n        options.logger?.debug(`${subject} → ${code} in ${time}`)\n        res.end()\n      } finally {\n        if (!res.writableEnded) {\n          res.end()\n        }\n      }\n    },\n\n    waitUntil: () => Promise.all(awaiting),\n  }\n}\n\n/**\n * Builds a full URL from the provided incoming message. Note this function\n * is not safe as one can set has a host anything based on headers. It is\n * useful to build the fetch request full URL.\n */\nfunction getURL(req: IncomingMessage) {\n  const proto = (req.socket as any)?.encrypted ? 'https' : 'http'\n  return new URL(String(req.url), `${proto}://${String(req.headers.host)}`)\n}\n\n/**\n * Takes headers from IncomingMessage and transforms them into the signature\n * accepted by fetch. It simply folds headers into a single value when they\n * hold an array. For others it just copies the value.\n */\nfunction toRequestInitHeaders(req: IncomingMessage): RequestInit['headers'] {\n  return Object.keys(req.headers).map((key) => {\n    const value = req.headers[key]\n    return [key, Array.isArray(value) ? value.join(', ') : value ?? '']\n  })\n}\n\n/**\n * Transforms WHATWG Headers into a Node Headers shape. Copies all items but\n * does a special case for Set-Cookie using the [`getSetCookie`](https://developer.mozilla.org/en-US/docs/Web/API/Headers/getSetCookie) method.\n */\nfunction toNodeHeaders(headers?: Headers): NodeHeaders {\n  const result: NodeHeaders = {}\n  if (headers) {\n    for (const [key, value] of headers.entries()) {\n      result[key] = key === 'set-cookie' ? headers.getSetCookie() : value\n    }\n  }\n  return result\n}\n"]}