{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/server/index.ts"], "names": [], "mappings": ";;;AAAA,+CAGuB;AAFrB,+HAAA,+BAA+B,OAAA;AAC/B,wHAAA,wBAAwB,OAAA;AAE1B,mDAAgD;AAAvC,+GAAA,aAAa,OAAA;AACtB,2CAA2D;AAAlD,uGAAA,SAAS,OAAA", "sourcesContent": ["export {\n  consumeUint8ArrayReadableStream,\n  pipeBodyStreamToResponse,\n} from './body-streams'\nexport { createHandler } from './create-handler'\nexport { runServer, EdgeRuntimeServer } from './run-server'\n"]}