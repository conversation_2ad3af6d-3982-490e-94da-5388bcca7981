{"version": 3, "file": "run-server.js", "sourceRoot": "", "sources": ["../../src/server/run-server.ts"], "names": [], "mappings": ";;;;;;AAAA,qDAAyD;AAEzD,gEAAiC;AACjC,gDAAuB;AAoBvB;;;;GAIG;AACI,KAAK,UAAU,SAAS,CAC7B,OAAyC;IAEzC,IAAI,OAAO,CAAC,IAAI,KAAK,SAAS;QAAE,OAAO,CAAC,IAAI,GAAG,CAAC,CAAA;IAChD,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,IAAA,8BAAa,EAAC,OAAO,CAAC,CAAA;IACrD,MAAM,MAAM,GAAG,cAAI,CAAC,YAAY,CAAC,OAAO,CAAC,CAAA;IACzC,MAAM,GAAG,GAAG,MAAM,IAAA,sBAAM,EAAC,MAAM,EAAE,OAAO,CAAC,CAAA;IAEzC,OAAO;QACL,GAAG,EAAE,MAAM,CAAC,GAAG,CAAC;QAChB,KAAK,EAAE,KAAK,IAAI,EAAE;YAChB,MAAM,SAAS,EAAE,CAAA;YACjB,MAAM,IAAI,OAAO,CAAO,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;gBAC1C,OAAO,MAAM,CAAC,KAAK,CAAC,CAAC,GAAG,EAAE,EAAE;oBAC1B,IAAI,GAAG;wBAAE,MAAM,CAAC,GAAG,CAAC,CAAA;oBACpB,OAAO,EAAE,CAAA;gBACX,CAAC,CAAC,CAAA;YACJ,CAAC,CAAC,CAAA;QACJ,CAAC;QACD,SAAS;KACV,CAAA;AACH,CAAC;AArBD,8BAqBC", "sourcesContent": ["import { createHand<PERSON>, Options } from './create-handler'\nimport type { EdgeContext } from '@edge-runtime/vm'\nimport listen from 'async-listen'\nimport http from 'http'\nimport type { ListenOptions } from 'net'\n\ninterface ServerOptions<T extends EdgeContext> extends Options<T> {}\n\nexport interface EdgeRuntimeServer {\n  /**\n   * The server URL.\n   */\n  url: string\n  /**\n   * Waits for all the current effects and closes the server.\n   */\n  close: () => Promise<void>\n  /**\n   * Waits for all current effects returning their result.\n   */\n  waitUntil: () => Promise<any[]>\n}\n\n/**\n * This helper will create a handler based on the given options and then\n * immediately run a server on the provided port. If there is no port, the\n * server will use a random one.\n */\nexport async function runServer<T extends EdgeContext>(\n  options: ListenOptions & ServerOptions<T>,\n): Promise<EdgeRuntimeServer> {\n  if (options.port === undefined) options.port = 0\n  const { handler, waitUntil } = createHandler(options)\n  const server = http.createServer(handler)\n  const url = await listen(server, options)\n\n  return {\n    url: String(url),\n    close: async () => {\n      await waitUntil()\n      await new Promise<void>((resolve, reject) => {\n        return server.close((err) => {\n          if (err) reject(err)\n          resolve()\n        })\n      })\n    },\n    waitUntil,\n  }\n}\n"]}