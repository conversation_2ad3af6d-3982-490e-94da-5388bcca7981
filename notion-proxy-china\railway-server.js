/**
 * Railway 部署版本 - Node.js Express服务器
 * 适用于需要更多控制的场景
 */

const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');

const app = express();
const PORT = process.env.PORT || 3000;

// 配置区
const NOTION_API_HOST = 'https://www.notion.so';
const IMAGE_SITES = {
  'notion-img': 'https://img.notionusercontent.com',
  'notion-file': 'https://file.notion.so',
  'notion-static': 'https://secure.notion-static.com',
  'aws-s3': 'https://prod-files-secure.s3.us-west-2.amazonaws.com',
  's3-static': 'https://s3-us-west-2.amazonaws.com'
};

// 中间件
app.use(cors());
app.use(express.json());

// 健康检查
app.get('/', (req, res) => {
  res.send(`
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Notion 图片代理服务 - Railway版</title>
        <style>
            body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; background: #f5f5f5; }
            .container { background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0,0,0,0.1); }
            h1 { color: #333; margin-bottom: 10px; }
            .status { background: #d4edda; color: #155724; padding: 10px; border-radius: 6px; margin: 20px 0; text-align: center; }
            .usage { background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #007bff; }
            code { background: #e9ecef; padding: 2px 6px; border-radius: 4px; font-family: 'Courier New', monospace; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🖼️ Notion 图片代理服务</h1>
            <p>Railway部署版本 - 专为中国内地用户优化</p>
            
            <div class="status">
                ✅ 服务运行正常 | 🚀 Railway部署 | 🆓 免费额度
            </div>
            
            <div class="usage">
                <h3>📖 使用方法:</h3>
                <p>将 Notion 图片 URL 中的域名替换为本服务域名</p>
                <p><strong>代理URL格式:</strong></p>
                <code>https://your-app.railway.app/proxy/image/xxx</code>
            </div>
            
            <p style="text-align: center; color: #666; margin-top: 30px;">
                Powered by Railway | Node.js Express
            </p>
        </div>
    </body>
    </html>
  `);
});

// 图片代理路由
Object.keys(IMAGE_SITES).forEach(prefix => {
  app.use(`/proxy/${prefix}`, createProxyMiddleware({
    target: IMAGE_SITES[prefix],
    changeOrigin: true,
    pathRewrite: {
      [`^/proxy/${prefix}`]: ''
    },
    onProxyReq: (proxyReq, req, res) => {
      // 添加优化的请求头
      proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36');
      proxyReq.setHeader('Referer', 'https://www.notion.so/');
      proxyReq.setHeader('Accept', 'image/webp,image/avif,image/apng,image/*,*/*;q=0.8');
      proxyReq.setHeader('Accept-Language', 'zh-CN,zh;q=0.9,en;q=0.8');
    },
    onProxyRes: (proxyRes, req, res) => {
      // 添加缓存头
      proxyRes.headers['Cache-Control'] = 'public, max-age=2592000'; // 30天
      proxyRes.headers['Access-Control-Allow-Origin'] = '*';
    },
    onError: (err, req, res) => {
      console.error('Proxy error:', err);
      res.status(502).send('代理请求失败');
    }
  }));
});

// AWS S3 特殊处理
app.get('/proxy/https%3A%2F%2F*', (req, res) => {
  const decodedUrl = decodeURIComponent(req.path.substring('/proxy/'.length));
  
  const proxy = createProxyMiddleware({
    target: decodedUrl,
    changeOrigin: true,
    pathRewrite: {
      '^/proxy/https%3A%2F%2F.*': ''
    },
    onProxyReq: (proxyReq, req, res) => {
      proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
      proxyReq.setHeader('Accept', 'image/webp,image/avif,image/apng,image/*,*/*;q=0.8');
    },
    onProxyRes: (proxyRes, req, res) => {
      proxyRes.headers['Cache-Control'] = 'public, max-age=2592000';
      proxyRes.headers['Access-Control-Allow-Origin'] = '*';
    }
  });
  
  proxy(req, res);
});

// 通用Notion代理
app.use('/proxy', createProxyMiddleware({
  target: NOTION_API_HOST,
  changeOrigin: true,
  pathRewrite: {
    '^/proxy': ''
  },
  onProxyReq: (proxyReq, req, res) => {
    proxyReq.setHeader('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    proxyReq.setHeader('Accept-Language', 'zh-CN,zh;q=0.9,en;q=0.8');
  },
  onProxyRes: (proxyRes, req, res) => {
    proxyRes.headers['Access-Control-Allow-Origin'] = '*';
    delete proxyRes.headers['content-security-policy'];
  }
}));

// 启动服务器
app.listen(PORT, () => {
  console.log(`🚀 Notion代理服务已启动在端口 ${PORT}`);
  console.log(`📖 访问 http://localhost:${PORT} 查看服务状态`);
});

module.exports = app;
