{"rewrites": [{"source": "/image/(.*)", "destination": "/api/proxy-node/image/$1"}, {"source": "/file/(.*)", "destination": "/api/proxy-node/file/$1"}, {"source": "/https%3A%2F%2F(.*)", "destination": "/api/proxy-node/https%3A%2F%2F$1"}], "headers": [{"source": "/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=2592000"}, {"key": "Access-Control-Allow-Origin", "value": "*"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}