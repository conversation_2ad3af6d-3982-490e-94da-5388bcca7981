# 🚀 中国内地免费部署方案详细指南

## 📋 方案对比

| 平台 | 优势 | 缺点 | 推荐度 |
|------|------|------|--------|
| **Vercel** | 🥇 亚洲节点快、免费额度大、部署简单 | 需要GitHub | ⭐⭐⭐⭐⭐ |
| **Netlify** | 🥈 全球CDN、免费SSL | 亚洲节点较少 | ⭐⭐⭐⭐ |
| **Deno Deploy** | 🥉 全功能、TypeScript原生 | 生态较新 | ⭐⭐⭐ |
| **Railway** | 简单部署、Docker支持 | 主要美国节点 | ⭐⭐ |

---

## 🥇 方案1: Vercel (强烈推荐)

### 为什么选择Vercel？
- ✅ **新加坡、香港节点** - 中国内地访问速度最快
- ✅ **免费额度充足** - 每月100GB流量，1000万次请求
- ✅ **部署极简** - 连接GitHub自动部署
- ✅ **自定义域名** - 免费SSL证书

### 部署步骤

#### 步骤1: 准备GitHub仓库
```bash
cd C:\Users\<USER>\Desktop\notion-proxy-china
git init
git add .
git commit -m "Initial commit"
git remote add origin https://github.com/你的用户名/notion-proxy-china.git
git push -u origin main
```

#### 步骤2: 部署到Vercel
1. 访问 [vercel.com](https://vercel.com)
2. 使用GitHub登录
3. 点击 "New Project"
4. 选择 `notion-proxy-china` 仓库
5. 点击 "Deploy"

#### 步骤3: 获取域名
部署完成后，你会得到类似这样的域名：
```
https://notion-proxy-china-xxx.vercel.app
```

#### 步骤4: 测试服务
访问你的域名，应该看到代理服务页面。

---

## 🥈 方案2: Netlify

### 适配代码
创建 `netlify/edge-functions/proxy.js`:

```javascript
export default async (request, context) => {
  // 这里放入适配后的代理代码
  // 基本逻辑与Vercel版本相同
}

export const config = {
  path: "/proxy/*"
}
```

### 部署步骤
1. 访问 [netlify.com](https://netlify.com)
2. 连接GitHub仓库
3. 设置构建命令: `npm run build`
4. 发布目录: `dist`
5. 部署

---

## 🥉 方案3: Deno Deploy

### 适配代码
创建 `main.ts`:

```typescript
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"

serve(async (request: Request) => {
  // Deno版本的代理逻辑
  // 支持完整的Web API
})
```

### 部署步骤
1. 访问 [deno.com/deploy](https://deno.com/deploy)
2. 连接GitHub
3. 选择 `main.ts` 作为入口文件
4. 部署

---

## 🔧 配置你的NotionNext项目

部署完成后，修改你的NotionNext项目配置：

```javascript
// conf/image.config.js
module.exports = {
  NOTION_HOST: 'https://你的域名.vercel.app/api/proxy',
  NOTION_HOST_BACKUP: [
    'https://你的域名.vercel.app/api/proxy',
    'https://nos.zhiqiao.dpdns.org', // 你的Cloudflare Worker作为备用
    'https://www.notion.so'
  ]
}
```

```javascript
// next.config.js
domains: [
  'gravatar.com',
  'www.notion.so',
  '你的域名.vercel.app', // 添加你的新域名
  'nos.zhiqiao.dpdns.org',
  // ... 其他域名
]
```

---

## 📊 性能测试

部署完成后，访问测试页面验证性能：
```
https://你的域名.vercel.app/api/proxy
```

预期结果：
- 🚀 图片加载时间: 2-4秒 (vs 原始8-15秒)
- ⚡ 缓存命中: <100ms
- ✅ 成功率: 95%+

---

## 🎯 推荐配置

### 最佳实践
1. **主代理**: 你的Vercel域名
2. **备用代理**: 你的Cloudflare Worker
3. **最后备用**: 原始Notion域名

### 域名配置
如果有自定义域名，可以在Vercel中配置：
1. Vercel Dashboard > Settings > Domains
2. 添加你的域名
3. 配置DNS记录

---

## 🆘 常见问题

### Q: 部署失败怎么办？
A: 检查代码语法，确保所有文件都已提交到GitHub

### Q: 图片还是加载慢？
A: 检查域名配置是否正确，确保使用了新的代理域名

### Q: 免费额度够用吗？
A: Vercel每月100GB流量，对个人博客完全够用

### Q: 可以绑定自定义域名吗？
A: 可以，Vercel支持免费绑定自定义域名和SSL证书

---

## 📞 技术支持

如遇问题，可以：
1. 查看Vercel部署日志
2. 检查浏览器控制台错误
3. 测试代理URL是否正确响应
