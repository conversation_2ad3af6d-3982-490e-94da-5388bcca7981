<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片代理测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-image {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        .loading { background-color: #d1ecf1; color: #0c5460; }
        .url-display {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <h1>🖼️ Notion图片代理测试</h1>
    <p>测试不同代理服务的图片加载情况</p>

    <div class="test-container">
        <h3>代理1: nos.zhiqiao.dpdns.org (你的Worker)</h3>
        <div class="url-display" id="url1"></div>
        <div class="status loading" id="status1">测试中...</div>
        <img class="test-image" id="img1" style="display:none;" />
    </div>

    <div class="test-container">
        <h3>代理2: www.notion.so (原始)</h3>
        <div class="url-display" id="url2"></div>
        <div class="status loading" id="status2">测试中...</div>
        <img class="test-image" id="img2" style="display:none;" />
    </div>

    <script>
        // 测试用的Notion图片URL
        const testImageUrl = 'https://www.notion.so/image/attachment%3Ad7744410-2387-4a4f-ae2b-3b0df74c0fe3%3A21grog.png?table=block&id=222a5a8a-afda-800b-8f17-c9ddd9538bed&t=222a5a8a-afda-800b-8f17-c9ddd9538bed';

        // 代理服务器列表 - 测试你的自定义Worker
        const proxies = [
            'https://nos.zhiqiao.dpdns.org',
            'https://www.notion.so'
        ];

        // 测试每个代理
        proxies.forEach((proxy, index) => {
            const proxyIndex = index + 1;
            const proxiedUrl = testImageUrl.replace(/^https?:\/\/[^\/]+/, proxy);

            // 显示URL
            document.getElementById(`url${proxyIndex}`).textContent = proxiedUrl;

            // 测试图片加载
            const img = document.getElementById(`img${proxyIndex}`);
            const status = document.getElementById(`status${proxyIndex}`);

            const startTime = Date.now();

            img.onload = function() {
                const loadTime = Date.now() - startTime;
                status.className = 'status success';
                status.textContent = `✅ 加载成功 (${loadTime}ms)`;
                img.style.display = 'block';
            };

            img.onerror = function() {
                const loadTime = Date.now() - startTime;
                status.className = 'status error';
                status.textContent = `❌ 加载失败 (${loadTime}ms)`;
            };

            // 设置超时
            setTimeout(() => {
                if (status.textContent === '测试中...') {
                    status.className = 'status error';
                    status.textContent = '❌ 加载超时 (>15s)';
                }
            }, 15000);

            img.src = proxiedUrl;
        });
    </script>
</body>
</html>
