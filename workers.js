/**
 * NotionNext 全能加速 Worker (终极免费版)  绑定了我的自定义域名：https://nos.zhiqiao.dpdns.org
 *
 * 100% 免费，无需绑定 R2 或任何付费服务。
 * 通过强大的边缘缓存和智能图片优化，实现极致加速。
 *
 * 特性:
 * - [极致健壮] 使用 Cloudflare HTMLRewriter 重写 HTML，兼容复杂页面。
 * - [极致性能] 智能图片格式(WebP)与尺寸优化，大幅减少图片体积。
 * - [智能缓存] 强制将优化后的资源在边缘节点缓存长达30天，实现毫秒级重复加载。
 */

// --- 配置区 ---
const NOTION_API_HOST = 'https://www.notion.so';

// 请求限制配置 - 针对中国内地优化
const RATE_LIMIT_CONFIG = {
  maxRetries: 5, // 增加重试次数
  baseDelay: 500, // 减少初始延迟
  maxDelay: 5000,  // 减少最大延迟
  backoffMultiplier: 1.5, // 减少退避倍数
  timeout: 15000 // 15秒超时
};

const IMAGE_SITES = {
  'notion-img': 'https://img.notionusercontent.com',
  'notion-file': 'https://file.notion.so',
  'notion-static': 'https://secure.notion-static.com',
  'aws-s3': 'https://prod-files-secure.s3.us-west-2.amazonaws.com',
  's3-static': 'https://s3-us-west-2.amazonaws.com'
};

// 中国内地网络优化配置
const CHINA_OPTIMIZATION = {
  // 使用更快的DNS解析
  preferredDNS: ['*******', '*******'],
  // 连接超时优化
  connectTimeout: 10000,
  // 启用HTTP/2推送
  enableHttp2Push: true,
  // 压缩优化
  enableBrotli: true
};

const ALLOWED_PROXY_DOMAINS = [
  'notion.so', 'www.notion.so', 'notion.site',
  'img.notionusercontent.com', 'file.notion.so', 'images.unsplash.com',
  'prod-files-secure.s3.us-west-2.amazonaws.com',
  's3-us-west-2.amazonaws.com',
  'secure.notion-static.com',
  'amazonaws.com'
];

// --- 主逻辑 ---
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event));
});

async function handleRequest(event) {
  const { request } = event;
  const url = new URL(request.url);
  const pathname = url.pathname;
  const prefix = pathname.split('/')[1];

  // 路由 1: 图片、文件 (使用智能缓存)
  if (IMAGE_SITES[prefix]) {
    const targetDomain = IMAGE_SITES[prefix];
    const targetPath = pathname.substring(prefix.length + 2);
    const targetUrl = new URL(targetDomain + '/' + targetPath + url.search + url.hash);
    return proxyImageWithCache(request, targetUrl, event);
  }
  
  // 路由 2: AWS S3 图片特殊处理
  if (pathname.startsWith('/https%3A%2F%2Fprod-files-secure.s3.us-west-2.amazonaws.com') ||
      pathname.startsWith('/https%3A%2F%2Fs3-us-west-2.amazonaws.com')) {
    // 解码URL
    const decodedPath = decodeURIComponent(pathname.substring(1));
    const targetUrl = new URL(decodedPath);
    return proxyImageWithCache(request, targetUrl, event);
  }

  // 路由 3: 通用代理
  if (prefix === 'proxy') {
    const subpath = pathname.substring('/proxy/'.length);
    if (!subpath || subpath === '/') {
      return new Response(getProxyLandingPage(url.origin), { status: 200, headers: { 'Content-Type': 'text/html; charset=utf-8' } });
    }
    const targetUrlString = subpath + url.search + url.hash;
    if (targetUrlString.startsWith('http')) {
      const targetUrl = new URL(targetUrlString);
      // 对通过 proxy 代理的 HTML 和图片也进行优化
      return proxyAndCache(request, targetUrl, event, true);
    }
    return new Response('❌ /proxy/ 后必须是完整的 http/https 网址', { status: 400 });
  }

  // 路由 4: Notion API
  if (prefix === 'api') {
    const targetUrl = new URL(NOTION_API_HOST + pathname + url.search + url.hash);
    return proxyRequest(request, targetUrl); // API 请求通常不缓存
  }

  // 路由 5: 根路径重定向
  if (pathname === '/' || pathname === '') {
     return Response.redirect("https://wobshare.us.kg/", 302);
  }

  // 兜底路由: 代理到 Notion
  const defaultTargetUrl = new URL(NOTION_API_HOST + pathname + url.search + url.hash);
  return proxyAndCache(request, defaultTargetUrl, event, false);
}


// --- 核心函数 ---

// 通用代理和缓存函数 (处理 HTML 等)
async function proxyAndCache(request, targetUrl, event, checkWhitelist) {
  if (checkWhitelist) {
    const isAllowed = ALLOWED_PROXY_DOMAINS.some(domain => targetUrl.hostname === domain || targetUrl.hostname.endsWith('.' + domain));
    if (!isAllowed) {
      return new Response(`⛔️ 禁止代理此域名: ${targetUrl.hostname}。`, { status: 403 });
    }
  }

  const cache = caches.default;
  const cacheKey = new Request(request.url, request);
  let response = await cache.match(cacheKey);

  if (!response) {
    console.log(`[Cache] MISS: ${request.url}`);
    let originalResponse = await proxyRequest(request, targetUrl);
    
    // 对 HTML 进行重写，添加hydration优化
    const contentType = originalResponse.headers.get('Content-Type') || '';
    if (contentType.includes('text/html')) {
        originalResponse = await rewriteHtml(originalResponse, targetUrl, new URL(request.url).origin);

        // 为HTML添加特殊的缓存头，减少hydration不匹配
        const modifiedResponse = new Response(originalResponse.body, originalResponse);
        modifiedResponse.headers.set('Cache-Control', 'public, max-age=3600, stale-while-revalidate=86400');
        modifiedResponse.headers.set('X-Hydration-Optimized', 'true');
        originalResponse = modifiedResponse;
    }

    if (request.method === 'GET' && originalResponse.ok) {
      // 深度定制缓存行为
      response = new Response(originalResponse.body, originalResponse);
      response.headers.set('Cache-Control', 'public, max-age=86400'); // HTML 等内容缓存1天
      event.waitUntil(cache.put(cacheKey, response.clone()));
    } else {
      response = originalResponse;
    }
  } else {
    console.log(`[Cache] HIT: ${request.url}`);
  }
  return response;
}

// [核心] 专门为图片优化的代理函数 - 中国内地优化版
async function proxyImageWithCache(request, targetUrl, event) {
  // 1. 生成包含优化参数的最终URL，并以此作为缓存键
  const optimizedUrl = new URL(targetUrl);
  const acceptHeader = request.headers.get('Accept') || '';

  // 优先使用WebP格式以减少传输量
  if (acceptHeader.includes('image/webp') || acceptHeader.includes('image/*')) {
    optimizedUrl.searchParams.set('format', 'webp');
  }

  // 添加中国内地网络优化参数
  optimizedUrl.searchParams.set('cache-control', 'max-age=31536000');
  addOptimizedImageParams(optimizedUrl, request.headers);

  // 使用优化后的 URL 创建缓存键，确保不同优化的图片有不同缓存
  const cacheKey = new Request(optimizedUrl.toString(), request);
  const cache = caches.default;
  let response = await cache.match(cacheKey);

  if (!response) {
    console.log(`[Image Cache] MISS: ${optimizedUrl.toString()}`);

    // 添加请求头以减少被限制的可能性 - 中国内地优化
    const modifiedRequest = new Request(optimizedUrl, {
      method: request.method,
      headers: {
        ...Object.fromEntries(request.headers),
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Referer': 'https://www.notion.so/',
        'Accept': 'image/webp,image/avif,image/apng,image/*,*/*;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache',
        'Sec-Fetch-Dest': 'image',
        'Sec-Fetch-Mode': 'no-cors',
        'Sec-Fetch-Site': 'cross-site'
      }
    });

    // 如果缓存未命中，才去请求源站
    response = await proxyRequest(modifiedRequest, optimizedUrl);

    if (response.ok) {
      const cacheableResponse = new Response(response.body, response);
      // 为图片设置超长缓存时间
      cacheableResponse.headers.set('Cache-Control', 'public, max-age=2592000'); // 缓存30天
      cacheableResponse.headers.set('Access-Control-Allow-Origin', '*');
      event.waitUntil(cache.put(cacheKey, cacheableResponse.clone()));
      return cacheableResponse;
    } else if (response.status === 429) {
      // 如果是429错误，返回占位图片并缓存较短时间
      const placeholderResponse = createPlaceholderImageResponse();
      placeholderResponse.headers.set('Cache-Control', 'public, max-age=300'); // 缓存5分钟
      event.waitUntil(cache.put(cacheKey, placeholderResponse.clone()));
      return placeholderResponse;
    }
  } else {
    console.log(`[Image Cache] HIT: ${optimizedUrl.toString()}`);
  }
  return response;
}


// 基础请求转发函数
async function proxyRequest(request, targetUrl, retryCount = 0) {
    const modifiedRequest = new Request(targetUrl, {
        body: request.body,
        headers: request.headers,
        method: request.method,
        redirect: 'follow'
    });

    try {
        const response = await fetch(modifiedRequest);

        // 处理429错误（请求过多）
        if (response.status === 429 && retryCount < RATE_LIMIT_CONFIG.maxRetries) {
            console.log(`[Retry] 429 error, retrying... (${retryCount + 1}/${RATE_LIMIT_CONFIG.maxRetries})`);
            // 指数退避：等待时间递增
            const delay = Math.min(
                RATE_LIMIT_CONFIG.baseDelay * Math.pow(RATE_LIMIT_CONFIG.backoffMultiplier, retryCount),
                RATE_LIMIT_CONFIG.maxDelay
            );
            await new Promise(resolve => setTimeout(resolve, delay));
            return proxyRequest(request, targetUrl, retryCount + 1);
        }

        // 如果是429错误且重试次数已达上限，返回缓存的占位图片
        if (response.status === 429) {
            console.log(`[Error] 429 error after max retries for: ${targetUrl}`);
            // 检查是否是图片请求
            const contentType = response.headers.get('Content-Type') || '';
            if (contentType.includes('image/') || targetUrl.toString().match(/\.(jpg|jpeg|png|gif|webp|svg)$/i)) {
                return createPlaceholderImageResponse();
            }
            // 对于非图片请求，返回错误信息
            return new Response('⚠️ 请求过于频繁，请稍后再试', {
                status: 429,
                headers: { 'Content-Type': 'text/plain; charset=utf-8' }
            });
        }

        const modifiedResponse = new Response(response.body, response);
        modifiedResponse.headers.set('Access-Control-Allow-Origin', '*');
        modifiedResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
        modifiedResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
        modifiedResponse.headers.delete('Content-Security-Policy');
        modifiedResponse.headers.delete('X-Content-Security-Policy');
        return modifiedResponse;
    } catch (e) {
        console.log(`[Error] Proxy request failed: ${e.message}`);
        return new Response(`❌ 代理请求失败: ${e.message}`, { status: 502 });
    }
}


// --- 辅助函数 ---

// 创建占位图片响应
function createPlaceholderImageResponse() {
    // 创建一个简单的1x1像素透明PNG图片
    const placeholderImage = new Uint8Array([
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, 0x00, 0x00, 0x00, 0x0D,
        0x49, 0x48, 0x44, 0x52, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x01,
        0x08, 0x06, 0x00, 0x00, 0x00, 0x1F, 0x15, 0xC4, 0x89, 0x00, 0x00, 0x00,
        0x0A, 0x49, 0x44, 0x41, 0x54, 0x78, 0x9C, 0x63, 0x00, 0x01, 0x00, 0x00,
        0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, 0x00, 0x00, 0x00, 0x00, 0x49,
        0x45, 0x4E, 0x44, 0xAE, 0x42, 0x60, 0x82
    ]);

    return new Response(placeholderImage, {
        status: 200,
        headers: {
            'Content-Type': 'image/png',
            'Cache-Control': 'public, max-age=3600', // 缓存1小时
            'Access-Control-Allow-Origin': '*'
        }
    });
}
// HTML 重写函数 - 优化版本，减少hydration不匹配
async function rewriteHtml(response, base, origin) {
  const proxyPrefix = `${origin}/proxy/`;
  const rewriter = new HTMLRewriter()
    .on('a, link', {
      element(element) {
        const attribute = 'href';
        const originalUrl = element.getAttribute(attribute);
        if (originalUrl) {
          const rewrittenUrl = rewriteUrl(originalUrl, base, proxyPrefix);
          element.setAttribute(attribute, rewrittenUrl);
        }
      },
    })
    .on('img', {
      element(element) {
        const originalSrc = element.getAttribute('src');
        if (originalSrc) {
          const rewrittenSrc = rewriteImageUrl(originalSrc, base, origin);
          element.setAttribute('src', rewrittenSrc);

          // 添加loading="lazy"和错误处理，减少hydration问题
          if (!element.getAttribute('loading')) {
            element.setAttribute('loading', 'lazy');
          }

          // 添加onerror处理，防止图片加载失败影响hydration
          const onError = `this.onerror=null;this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjBmMGYwIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkltYWdlIE5vdCBGb3VuZDwvdGV4dD48L3N2Zz4=';`;
          element.setAttribute('onerror', onError);
        }
      },
    })
    .on('script, source, iframe, video', {
      element(element) {
        const attribute = 'src';
        const originalUrl = element.getAttribute(attribute);
        if (originalUrl) {
          element.setAttribute(attribute, rewriteUrl(originalUrl, base, proxyPrefix));
        }
      },
    })
    // 添加meta标签和预加载来改善hydration和性能
    .on('head', {
      element(element) {
        element.append('<meta name="viewport" content="width=device-width, initial-scale=1">', { html: true });
        element.append('<meta name="hydration-fix" content="true">', { html: true });
        // 添加图片预加载提示
        element.append('<link rel="preconnect" href="https://prod-files-secure.s3.us-west-2.amazonaws.com">', { html: true });
        element.append('<link rel="preconnect" href="https://img.notionusercontent.com">', { html: true });
        element.append('<link rel="dns-prefetch" href="https://s3-us-west-2.amazonaws.com">', { html: true });
        // 添加图片加载优化脚本
        element.append(`
          <script>
            // 图片加载优化脚本
            (function() {
              const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                  if (entry.isIntersecting) {
                    const img = entry.target;
                    if (img.dataset.src) {
                      img.src = img.dataset.src;
                      img.removeAttribute('data-src');
                      observer.unobserve(img);
                    }
                  }
                });
              }, { rootMargin: '50px' });

              document.addEventListener('DOMContentLoaded', () => {
                document.querySelectorAll('img[data-src]').forEach(img => observer.observe(img));
              });
            })();
          </script>
        `, { html: true });
      },
    });
  return rewriter.transform(response);
}

// URL 重写辅助函数
function rewriteUrl(originalUrl, base, proxyPrefix) {
    try {
        if (originalUrl.startsWith('data:') || originalUrl.startsWith('http')) {
            return originalUrl;
        }
        const fullUrl = new URL(originalUrl, base.toString()).toString();
        // 只有在代理模式下才添加 proxy 前缀
        if (proxyPrefix.includes('/proxy/')) {
            return `${proxyPrefix}${fullUrl}`;
        }
        return fullUrl; // 对于非代理的HTML（例如直接访问根路径），不需要加前缀
    } catch (e) {
        return originalUrl;
    }
}

// 专门处理图片URL的重写函数
function rewriteImageUrl(originalUrl, base, origin) {
    try {
        if (originalUrl.startsWith('data:')) {
            return originalUrl;
        }

        // 如果已经是完整的HTTP URL
        if (originalUrl.startsWith('http')) {
            const url = new URL(originalUrl);

            // 检查是否是AWS S3图片
            if (url.hostname.includes('amazonaws.com') ||
                url.hostname.includes('prod-files-secure.s3.us-west-2.amazonaws.com')) {
                // 直接使用我们的代理域名处理AWS S3图片
                return `${origin}/${encodeURIComponent(originalUrl)}`;
            }

            // 检查是否是Notion图片
            if (url.hostname.includes('notion')) {
                const pathParts = url.pathname.split('/');
                if (pathParts.length > 1) {
                    const prefix = getNotionPrefix(url.hostname);
                    if (prefix) {
                        return `${origin}/${prefix}${url.pathname}${url.search}`;
                    }
                }
            }

            return originalUrl;
        }

        // 相对URL处理
        const fullUrl = new URL(originalUrl, base.toString()).toString();
        return rewriteImageUrl(fullUrl, base, origin);
    } catch (e) {
        return originalUrl;
    }
}

// 获取Notion域名对应的前缀
function getNotionPrefix(hostname) {
    if (hostname.includes('img.notionusercontent.com')) return 'notion-img';
    if (hostname.includes('file.notion.so')) return 'notion-file';
    if (hostname.includes('secure.notion-static.com')) return 'notion-static';
    return null;
}

// 图片参数优化函数
function addOptimizedImageParams(targetUrl, requestHeaders) {
  const optimizableDomains = [
    'img.notionusercontent.com',
    'file.notion.so',
    'images.unsplash.com',
    'prod-files-secure.s3.us-west-2.amazonaws.com',
    's3-us-west-2.amazonaws.com'
  ];

  if (optimizableDomains.includes(targetUrl.hostname)) {
    const userAgent = requestHeaders.get('User-Agent') || '';
    let width;
    if (/iPhone|Android.*Mobile/.test(userAgent)) width = 750;
    else if (/iPad|Tablet/.test(userAgent)) width = 1200;
    else width = 1920;

    // 对于AWS S3，使用不同的参数名
    if (targetUrl.hostname.includes('amazonaws.com')) {
      // AWS S3 不支持width参数，但我们可以添加其他优化参数
      if (width) {
        targetUrl.searchParams.set('response-cache-control', 'max-age=31536000');
      }
    } else if (width) {
      targetUrl.searchParams.set('width', width);
    }
  }
}

// 门户页面 HTML (代码省略以保持简洁)
function getProxyLandingPage(origin) {
    const wobshareUrl = "https://wobshare.us.kg/";
    return `
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>代理服务门户</title>
        <!-- ... styles and other head content ... -->
    </head>
    <body>
        <!-- ... body content and scripts ... -->
    </body>
    </html>
    `;
}